

import os
from kafka import KafkaConsumer
from httpx import Client
# Load Kafka config from environment variables
KAFKA_ENABLED = os.getenv("KAFKA_ENABLED", "true").lower() == "true"
KAFKA_URL = os.getenv("KAFKA_URL", "kafka.default.svc.cluster.local:9092")
KAFKA_TOPIC_ES_CREATED_FILE = os.getenv("KAFKA_TOPIC_ES_CREATED_FILE", "es_created_File")
KAFKA_CONSUMER_GROUP = os.getenv("KAFKA_CONSUMER_GROUP", "face-validation")
KAFKA_AUTO_OFFSET_RESET = os.getenv("KAFKA_AUTO_OFFSET_RESET", "latest")
DOCSERVER_V2_BASEURL = os.getenv("DOCSERVER_V2_URL", "http://cyber-docserver-v2-microservice.cyber.svc.cluster.local")

from pydantic import BaseModel, Field
from typing import Optional
import json

class FilePayload(BaseModel):
    file_name: Optional[str] = Field(None, alias="fileName")
    es_id: Optional[str] = Field(None, alias="es-id")
    file_type: Optional[str] = Field(None, alias="fileType")


class FileMessage(BaseModel):
    payload: FilePayload


def get_document(file_id: str, download: bool = False):
    fieldsToReturn = "mimeType"
    # if download:
    #     download="download=true&return_url=true"
    
    # http://cyber-docserver-v2-microservice.cyber.svc.cluster.local/files/f75a7727163d803ee1b981f6e9487dfc?download=false&fieldsToReturn=mimeType&return_url=false

    if not download:
        with Client(base_url=DOCSERVER_V2_BASEURL) as client:
            response = client.get(f"/files/{file_id}?fieldsToReturn={fieldsToReturn}")
            return response.json()
        
    with Client(base_url=DOCSERVER_V2_BASEURL, follow_redirects=True) as client:
        response = client.get(f"/files/{file_id}?download={download}&fieldsToReturn={fieldsToReturn}")
        return response.content

image_mimetypes = [
    "image/jpeg",  # Standard JPEG image
    "image/pjpeg", # Progressive JPEG (less common)
    "image/x-png", # PNG image (alternative MIME type)
    "image/png"    # Standard PNG image
]



if KAFKA_ENABLED:
    consumer = KafkaConsumer(
        KAFKA_TOPIC_ES_CREATED_FILE,
        bootstrap_servers=KAFKA_URL,
        group_id=KAFKA_CONSUMER_GROUP,
        auto_offset_reset=KAFKA_AUTO_OFFSET_RESET,
        enable_auto_commit=True,
        value_deserializer=lambda x: x.decode("utf-8"),
    )



print(f"Listening to topic: {KAFKA_TOPIC_ES_CREATED_FILE}")

for message in consumer:
    msg = message.value
    data = None
    try:
        data = FileMessage(**json.loads(msg))
        print(data)
    except Exception:
        pass
    
    if not data or not data.payload.es_id:
        continue
    
    
    file_document = get_document(data.payload.es_id)
    print(file_document)
# else:
#     print("Kafka is disabled.")


get_document("f75a7727163d803ee1b981f6e9487dfc", download=False)